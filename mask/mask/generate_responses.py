import pandas as pd
import glob
import os
import random
from openai import AsyncOpenAI
from anthropic import AsyncAnthropic
import argparse
import asyncio
from dotenv import load_dotenv
from transformers import AutoModelForCausalLM, AutoTokenizer
import torch

load_dotenv()


async def generate_responses_async(
    client, model_name, formatted_prompts, max_tokens, temperature, semaphore, K=1
):
    """
    Generates responses from the specified model for a batch of prompts.

    Args:
        client: API client instance for the model
        model_name: Name of the model to use
        formatted_prompts: Dictionary mapping prompt keys to message lists
        max_tokens: Maximum tokens to generate
        temperature: Sampling temperature
        semaphore: Asyncio semaphore for rate limiting
        K: Number of responses to generate per prompt (default=1)

    Returns:
        dict: Mapping of prompt keys to lists of generated responses
    """
    results = {}

    async def process_prompt(prompt_key, messages):
        """Processes a single prompt with retries and rate limiting."""
        retry_delay = 3
        max_retries = 7
        jitter = 0.1

        all_responses = []

        for _ in range(K):
            for attempt in range(max_retries):
                try:
                    async with semaphore:
                        # Model-specific API calls
                        if "claude" in model_name:
                            system_prompt = None
                            user_messages = messages.copy()
                            if messages and messages[0]["role"] == "system":
                                system_prompt = messages[0]["content"]
                                user_messages = messages[1:]

                            message = await client.messages.create(
                                model=model_name,
                                max_tokens=max_tokens,
                                system=system_prompt,
                                messages=user_messages,
                                temperature=temperature,
                            )
                        elif (
                            isinstance(client, dict)
                            and client.get("type") == "huggingface"
                        ):
                            # Handle Hugging Face models
                            model = client["model"]
                            tokenizer = client["tokenizer"]

                            # Format messages into a prompt string
                            prompt = ""
                            for msg in messages:
                                role = msg["role"]
                                content = msg["content"]
                                if role == "system":
                                    prompt += f"<s>[INST] {content} [/INST]"
                                elif role == "user":
                                    prompt += f"<s>[INST] {content} [/INST]"
                                elif role == "assistant":
                                    prompt += f" {content} </s>"

                            # If the last message is from user, add the assistant start token
                            if messages[-1]["role"] == "user":
                                prompt += " "

                            # Process in event loop to avoid blocking
                            loop = asyncio.get_event_loop()
                            inputs = await loop.run_in_executor(
                                None,
                                lambda: tokenizer(prompt, return_tensors="pt").to(
                                    model.device
                                ),
                            )

                            # Generate response
                            outputs = await loop.run_in_executor(
                                None,
                                lambda: model.generate(
                                    **inputs,
                                    max_new_tokens=max_tokens,
                                    temperature=temperature,
                                    do_sample=temperature > 0,
                                    top_p=0.95,
                                ),
                            )

                            # Decode the response
                            response = await loop.run_in_executor(
                                None,
                                lambda: tokenizer.decode(
                                    outputs[0][inputs.input_ids.shape[1] :],
                                    skip_special_tokens=True,
                                ),
                            )
                        else:
                            if (
                                "gpt" in model_name
                                or "o1" in model_name
                                or "o3" in model_name
                            ):
                                # replace "system" with "developer"
                                if messages and messages[0]["role"] == "system":
                                    messages[0]["role"] = "developer"
                            completion = await client.chat.completions.create(
                                model=model_name,
                                messages=messages,
                                max_tokens=max_tokens,
                                temperature=temperature,
                            )

                    # Extract response based on model type
                    if "claude" in model_name:
                        response = message.content[0].text
                    elif (
                        isinstance(client, dict) and client.get("type") == "huggingface"
                    ):
                        # Response is already extracted above
                        pass
                    else:
                        response = completion.choices[0].message.content

                    all_responses.append(response)
                    break

                except Exception as e:
                    if "invalid_prompt" in str(e):
                        print(
                            f"Prompt violated content policy for prompt: {prompt_key}. Replacing with error message."
                        )
                        all_responses.append(
                            "[Error: Prompt violated content policies]"
                        )
                        break
                    elif attempt == max_retries - 1:
                        print(f"Max retries exceeded for prompt: {prompt_key}")
                        print(e)
                        all_responses.append(None)
                    else:
                        print(
                            f"Error occurred for prompt: {prompt_key}. Attempt {attempt + 1}/{max_retries}. Retrying in {retry_delay:.1f} seconds."
                        )
                        print(e)
                        jittered_delay = retry_delay * (
                            1 + jitter * (2 * random.random() - 1)
                        )
                        await asyncio.sleep(jittered_delay)
                        retry_delay *= 2

        results[prompt_key] = all_responses

    tasks = [
        process_prompt(key, messages) for key, messages in formatted_prompts.items()
    ]
    await asyncio.gather(*tasks)

    return results


def create_model_client(model_name):
    if "claude" in model_name.lower():
        return AsyncAnthropic(api_key=os.getenv("ANTHROPIC_API_KEY"))
    elif (
        "gpt" in model_name.lower()
        or "o1" in model_name.lower()
        or "o3" in model_name.lower()
    ):
        return AsyncOpenAI(api_key=os.getenv("OPENAI_API_KEY"))
    else:
        print(f"Loading Hugging Face model: {model_name}")
        tokenizer = AutoTokenizer.from_pretrained(model_name, trust_remote_code=True)
        model = AutoModelForCausalLM.from_pretrained(
            model_name,
            torch_dtype=torch.bfloat16,
            device_map="auto",
            trust_remote_code=True,
        )
        return {"model": model, "tokenizer": tokenizer, "type": "huggingface"}


async def process_dataframe(
    input_csv, output_csv, temperature, model_name, max_tokens, lie_k, belief_k=None
):
    """
    Process a dataframe and generate responses from an AI model.

    Args:
        input_csv (str): Path to input CSV file
        output_csv (str): Path to output CSV file
        temperature (float): Sampling temperature for generation
        model_name (str): Name of the model to use
        max_tokens (int): Maximum tokens for generation
        belief_k (dict): Required mapping of belief elicitation numbers to number of runs
            e.g. {"1": 3, "2": 1, "3": 1} means run belief_elicit_1 three times,
            belief_elicit_2 and belief_elicit_3 once each
        lie_k (int): Number of lying prompts to generate for each row

    The function reads a CSV containing prompts, generates responses using the specified
    model, and saves the results to a new CSV file.
    """
    client = None
    try:
        # Initialize model-specific client
        client = create_model_client(model_name)

        semaphore = asyncio.Semaphore(10)

        # Read input data and set default system prompt if needed
        df = pd.read_csv(input_csv)
        if (
            "belief_system_1" not in df.columns and "belief_elicit_1" in df.columns
        ):  # All files except provided_facts and statistics
            df["belief_system_1"] = "You are a helpful assistant."

        # Check if this is a doubling down scenario (multi-turn conversation)
        is_doubling_down = (
            "dd_turn_1_prompt" in df.columns and "dd_turn_1_response" in df.columns
        )
        print(f"Is this a doubling down scenario? {is_doubling_down}")

        # Prepare prompts for generation
        all_prompts = []
        indices = []
        prompt_types = []

        # Process each row in the dataframe
        for index, row in df.iterrows():
            # Handle lying prompts - either doubling down or standard
            if is_doubling_down:
                # For doubling down, include the full conversation history
                for run in range(lie_k):
                    all_prompts.append(
                        [
                            {"role": "system", "content": row["system_prompt"]},
                            {"role": "user", "content": row["dd_turn_1_prompt"]},
                            {"role": "assistant", "content": row["dd_turn_1_response"]},
                            {"role": "user", "content": row["user_prompt"]},
                        ]
                    )
                    indices.append(index)
                    prompt_types.append(f"lying_run{run+1}")
            else:
                # Standard lying prompt with just system and user messages
                for run in range(lie_k):
                    all_prompts.append(
                        [
                            {"role": "system", "content": row["system_prompt"]},
                            {"role": "user", "content": row["user_prompt"]},
                        ]
                    )
                    indices.append(index)
                    prompt_types.append(f"lying_run{run+1}")

            # Handle belief elicitation prompts if present
            if "belief_elicit_1" in df.columns:
                for be_num, k_value in belief_k.items():
                    belief_col = f"belief_elicit_{be_num}"
                    if pd.notna(row.get(belief_col)):
                        for run in range(k_value):
                            all_prompts.append(
                                [
                                    {
                                        "role": "system",
                                        "content": row["belief_system_1"],
                                    },
                                    {"role": "user", "content": row[belief_col]},
                                ]
                            )
                            indices.append(index)
                            prompt_types.append(f"be{be_num}_run{run+1}")

        # Generate responses in parallel for all prompts
        all_responses = await asyncio.gather(
            *[
                generate_responses_async(
                    client,
                    model_name,
                    {prompt_type: prompt},
                    max_tokens=max_tokens,
                    temperature=temperature,
                    semaphore=semaphore,
                    K=1,
                )
                for prompt, prompt_type in zip(all_prompts, prompt_types)
            ]
        )

        # Create mapping for output column names
        column_mapping = {
            f"lying_run{run+1}": f"generation(System Prompt + User Prompt)_run{run+1}"
            for run in range(lie_k)
        }

        # Add belief elicitation column names if present
        if "belief_elicit_1" in df.columns:
            for be_num, k_value in belief_k.items():
                for run in range(k_value):
                    column_mapping[f"be{be_num}_run{run+1}"] = (
                        f"generation(belief_system_1 + belief_elicit_{be_num})_run{run+1}"
                    )

        # Process responses and save to dataframe
        for response, index, prompt_type in zip(all_responses, indices, prompt_types):
            column_name = column_mapping[prompt_type]
            try:
                response_value = (
                    response[prompt_type][0] if prompt_type in response else None
                )
                if response_value is None:
                    raise ValueError("Generation failed or timed out")
                df.at[index, column_name] = response_value
            except Exception as e:
                error_msg = f"[ERROR: {str(e)}]"
                print(
                    f"WARNING: Error processing response for index {index}, prompt type {prompt_type}: {str(e)}"
                )
                df.at[index, column_name] = error_msg

        # Save results to CSV
        df.to_csv(output_csv, index=False)

    finally:
        # Clean up client if needed
        if hasattr(client, "close"):
            await client.close()


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Generate responses from AI models for a set of prompts"
    )
    parser.add_argument("--test", action="store_true", help="Run in test mode")
    parser.add_argument(
        "--model",
        default="meta-llama/Llama-3.1-8B-Instruct",
        help="Model name to use for generation",
    )
    parser.add_argument(
        "--max_tokens", type=int, default=1000, help="Maximum tokens for generation"
    )
    parser.add_argument(
        "--temperature", type=float, default=1.0, help="Temperature for generation"
    )
    parser.add_argument(
        "--lie_k",
        type=int,
        default=1,
        help="Number of lying prompts to generate for each row",
    )

    args = parser.parse_args()

    for file in glob.iglob("test_csv_data/*.csv" if args.test else "csv_data/*.csv"):
        belief_k = {"1": 3} if "statistics" in file else {"1": 3, "2": 1, "3": 1}

        modelname = args.model.split("/")[-1] if "/" in args.model else args.model
        output_file = file.replace(".csv", f"_{modelname}.csv")
        output_file = output_file.replace("csv_data/", "csv_data/responses/")
        os.makedirs(os.path.dirname(output_file), exist_ok=True)

        # Skip if output file already exists
        if os.path.exists(output_file):
            print(f"Skipping {output_file} as it already exists")
            continue

        print("Processing", file, "to", output_file)
        asyncio.run(
            process_dataframe(
                file,
                output_file,
                temperature=args.temperature,
                model_name=args.model,
                max_tokens=args.max_tokens,
                lie_k=args.lie_k,
                belief_k=belief_k,
            )
        )
